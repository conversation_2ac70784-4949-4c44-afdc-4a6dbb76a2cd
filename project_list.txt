存储目录：/Users/<USER>/Projects/idea
-work
--wdcloud
http://gitlab.wandatech-dev.com/wanda/nacos-config/gateway.gateway-admin
---member
http://gitlab.wandatech-dev.com/wanda/member/member_mission
---check-in
http://gitlab.wandatech-dev.com/wanda/interactive/check-in/check-in-admin-service
---plus-card
http://gitlab.wandatech-dev.com/wanda/card/plus-card
http://gitlab.wandatech-dev.com/wanda/nacos-config/plus-card
http://gitlab.wandatech-dev.com/wanda/api/admin-api/plus-card-admin-api
http://gitlab.wandatech-dev.com/wanda/api/front-api-b/plus-card-front-api
http://gitlab.wandatech-dev.com/wanda/api/front-api-b/plus-card-front-service
http://gitlab.wandatech-dev.com/commons/lark-support/cmc/lark-cmc-purchase-agreement-admin-service-contract
---star-card
http://gitlab.wandatech-dev.com/wanda/card/star-card
http://gitlab.wandatech-dev.com/wanda/nacos-config/star-card
http://gitlab.wandatech-dev.com/wanda/api/admin-api/star-card-admin-api
---voucher
http://gitlab.wandatech-dev.com/wanda/api/admin-api/voucher-type-admin-api
http://gitlab.wandatech-dev.com/wanda/voucher/type/voucher-type-consumer
http://gitlab.wandatech-dev.com/wanda/voucher/type/type-admin
http://gitlab.wandatech-dev.com/wanda/voucher/senddown/voucher-issue-task
http://gitlab.wandatech-dev.com/wanda/voucher/senddown/voucher-issue-consumer
http://gitlab.wandatech-dev.com/wanda/voucher/senddown/voucher-issue-admin-service
---datasync
http://gitlab.wandatech-dev.com/wanda/datasync/datasync-common
http://gitlab.wandatech-dev.com/wanda/datasync/datasync-task
http://gitlab.wandatech-dev.com/wanda/datasync/datasync-consumer
http://gitlab.wandatech-dev.com/wanda/nacos-config/datasync
---external
http://gitlab.wandatech-dev.com/wanda/external-sys/external-all
http://gitlab.wandatech-dev.com/wanda/external-sys/external-common
http://gitlab.wandatech-dev.com/wanda/external-sys/external-api
http://gitlab.wandatech-dev.com/wanda/external-sys/external-admin-service
http://gitlab.wandatech-dev.com/wanda/external-sys/external-consumer
http://gitlab.wandatech-dev.com/wanda/external-sys/external-task
http://gitlab.wandatech-dev.com/wanda/nacos-config/external-sys
http://gitlab.wandatech-dev.com/commons/httpclients/feishu-approval-center-httpclient
---approval
http://gitlab.wandatech-dev.com/wanda/approval/approval-all
http://gitlab.wandatech-dev.com/wanda/approval/approval-task
http://gitlab.wandatech-dev.com/wanda/approval/approval-front
http://gitlab.wandatech-dev.com/wanda/approval/approval-admin
http://gitlab.wandatech-dev.com/wanda/approval/approval-consumer
http://gitlab.wandatech-dev.com/wanda/approval/approval-common
http://gitlab.wandatech-dev.com/wanda/api/admin-api/approval-admin-api
http://gitlab.wandatech-dev.com/wanda/nacos-config/approval
---support-lark-contract
----CMC
http://gitlab.wandatech-dev.com/commons/lark-support/cmc/lark-rec-voucher-type-admin-contract
http://gitlab.wandatech-dev.com/commons/lark-support/cmc/lark-cmc-purchase-agreement-admin-service-contract
--cmc
http://gitlab.mx.com/cmc-purchase/agreement
http://gitlab.mx.com/cmc-member/cmc-blacklist-parent
http://gitlab.mx.com/cmc-voucher/cmc-rec-voucher-type-parent
http://gitlab.mx.com/cmc-basic/external-sys
http://gitlab.mx.com/cmc-basic/cmc-datasync