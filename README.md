# GitLab项目批量克隆工具

这是一个用于批量下载GitLab项目代码的工具，可以根据指定的项目列表文件，自动克隆项目或项目组到指定的目录结构中。

## 功能特点

1. 支持从配置文件中读取项目列表
2. 支持多级目录结构
3. 自动识别项目和项目组
4. 对于项目组，自动克隆其下所有项目
5. 支持进度显示和错误处理
6. 自动处理目录名冲突，使用路径前缀区分相同名称的项目

## 环境要求

- Python 3.6+
- 依赖包：见 `requirements.txt`

## 安装

1. 克隆本仓库：

```bash
git clone <仓库地址>
cd git_project_batch_clone
```

2. 安装依赖：

```bash
pip install -r requirements.txt
```

3. 配置GitLab访问凭证：

在 `~/.zshrc` 或 `~/.bashrc` 中添加环境变量 `GITLAB_CONFIGS`，格式为JSON字符串：

```bash
export GITLAB_CONFIGS='{
  "gitlab.example.com": {
    "url": "https://gitlab.example.com",
    "token": "your_private_token"
  },
  "gitlab.another-example.com": {
    "url": "https://gitlab.another-example.com",
    "token": "another_private_token"
  }
}'
```

然后执行 `source ~/.zshrc` 或 `source ~/.bashrc` 使配置生效。

## 使用方法

### 1. 准备项目列表文件

创建一个项目列表文件（默认为 `project_list.txt`），格式如下：

```
存储目录：/path/to/store/projects
-目录1
--子目录1
项目地址1
项目地址2
--子目录2
项目地址3
-目录2
项目地址4
```

说明：
- 第一行必须是 `存储目录：` 开头，指定克隆项目的根目录
- 一个横杠 `-` 表示一级目录，两个横杠 `--` 表示二级目录，以此类推
- 目录行后面的非目录行被视为GitLab项目地址

### 2. 运行程序

```bash
python batch_clone.py [项目列表文件路径]
```

如果不指定项目列表文件路径，默认使用当前目录下的 `project_list.txt`。

#### 命令行参数

程序支持以下命令行参数：

```
-h, --help            显示帮助信息并退出
-v, --version         显示版本信息并退出
--dry-run             仅解析项目列表文件，不执行实际克隆操作
--timeout TIMEOUT     Git克隆操作的超时时间（秒），默认为120秒
--depth DEPTH         Git浅克隆的深度，默认为0（完整克隆）
--full-clone          执行完整克隆，不使用浅克隆（默认启用）
--no-full-clone       不执行完整克隆，使用浅克隆
--skip-existing       跳过已存在的目录，不尝试克隆（默认启用）
--no-skip-existing    不跳过已存在的目录，尝试克隆
--use-ssh             使用SSH URL而不是HTTP URL进行克隆
--prefer-ssh          优先使用SSH URL进行克隆（默认启用）
--no-prefer-ssh       不优先使用SSH URL进行克隆
--use-system-git      使用系统git命令而不是GitPython库进行克隆（默认启用）
--no-use-system-git   不使用系统git命令，使用GitPython库进行克隆
--log-level {DEBUG,INFO,WARNING,ERROR,CRITICAL}
                      日志级别，默认为INFO
--skip-projects SKIP_PROJECTS
                      跳过特定的项目，多个项目名称用逗号分隔
```

示例：

```bash
# 使用默认参数（现在默认使用SSH URL、系统git命令、跳过已存在目录、完整克隆）
python batch_clone.py

# 指定项目列表文件
python batch_clone.py my_project_list.txt

# 干运行模式，不执行实际克隆
python batch_clone.py --dry-run

# 设置超时时间为300秒
python batch_clone.py --timeout 300

# 使用浅克隆（深度为1）
python batch_clone.py --no-full-clone --depth 1

# 不跳过已存在的目录
python batch_clone.py --no-skip-existing

# 使用HTTP URL而不是SSH URL
python batch_clone.py --no-prefer-ssh

# 使用GitPython库而不是系统git命令
python batch_clone.py --no-use-system-git

# 设置日志级别为DEBUG，显示更详细的信息
python batch_clone.py --log-level DEBUG

# 跳过特定的项目
python batch_clone.py --skip-projects plus-card-common,plus-card-web
```

## 示例

假设有如下项目列表文件 `project_list.txt`：

```
存储目录：./downloaded_projects
-wdcloud
--plus-card
http://gitlab.wandatech-dev.com/wanda/card/plus-card
http://gitlab.wandatech-dev.com/wanda/nacos-config/plus-card

-cmc
--agreemnt
http://gitlab.mx.com/cmc-purchase/agreement
```

运行程序：

```bash
python batch_clone.py
```

程序将会：
1. 在当前目录下创建 `downloaded_projects` 目录
2. 在其中创建 `wdcloud` 和 `cmc` 目录
3. 在 `wdcloud` 目录下创建 `plus-card` 目录
4. 在 `cmc` 目录下创建 `agreemnt` 目录
5. 克隆指定的项目到相应的目录中

## 注意事项

1. 确保已正确配置GitLab访问凭证
2. 确保有足够的磁盘空间用于克隆项目
3. 对于大型项目组，克隆可能需要较长时间
4. 如果目标目录已存在，程序会跳过克隆
5. 当遇到相同名称的项目时，程序会自动使用路径前一级作为前缀，用横杠连接，例如：
   - `wanda/card/plus-card` 克隆为 `plus-card`
   - `wanda/nacos-config/plus-card` 克隆为 `nacos-config-plus-card`

## 故障排除

如果遇到问题，请检查：

1. GitLab访问凭证是否正确配置
2. 网络连接是否正常
3. 项目列表文件格式是否正确
4. 是否有足够的权限访问指定的GitLab项目

### 常见问题解决方案

1. **克隆过程卡住或克隆失败**：
   - 使用 `--timeout` 参数增加超时时间，例如 `--timeout 300`
   - 使用 `--no-full-clone --depth 1` 参数进行浅克隆
   - 程序默认已使用系统git命令和SSH URL，这通常能解决大多数克隆问题
   - 使用 `--log-level DEBUG` 参数查看更详细的日志，了解卡住的原因
   - 使用 `--skip-projects` 参数跳过已知有问题的项目
   - 检查网络连接是否稳定
   - 检查GitLab服务器是否可访问

2. **认证失败**：
   - 检查 `~/.zshrc` 或 `~/.bashrc` 中的GitLab访问凭证是否正确
   - 确保已执行 `source ~/.zshrc` 或 `source ~/.bashrc` 使配置生效
   - 程序默认已使用SSH URL，如果需要使用HTTP URL，请使用 `--no-prefer-ssh` 参数
   - 如果使用SSH URL，确保已正确配置SSH密钥
   - 程序默认已使用系统git命令，这通常能使用系统已配置的凭证

3. **目录已存在**：
   - 程序默认已跳过已存在的目录，如果需要覆盖已存在的目录，请使用 `--no-skip-existing` 参数
   - 手动删除目标目录后重试

4. **克隆失败，错误信息为 "bool() undefined when iterable == total == None"**：
   - 这是tqdm进度条与GitPython的兼容性问题，已在最新版本中修复
   - 更新到最新版本的程序，或使用不带进度条的版本
   - 程序默认已使用系统git命令，这可以绕过GitPython库的问题

5. **其他错误**：
   - 查看错误信息，根据具体情况处理
   - 查看日志文件 `batch_clone.log` 获取详细信息
   - 使用 `--log-level DEBUG` 参数获取更详细的日志
   - 尝试使用 `--dry-run` 参数先检查解析结果
   - 尝试手动克隆单个项目，查看是否有特定错误

## 许可证

MIT