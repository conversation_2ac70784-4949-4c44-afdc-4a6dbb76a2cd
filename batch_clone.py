#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import gitlab
import git
import argparse
import time
import threading
import subprocess
from pathlib import Path
from tqdm import tqdm
import parse_project_address_python as parser

# 添加日志模块
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("batch_clone.log"),
        logging.StreamHandler()
    ]
)

# 创建一个全局的日志对象
logger = logging.getLogger("batch_clone")

# 添加一个超时装饰器
def timeout(seconds):
    def decorator(func):
        def wrapper(*args, **kwargs):
            result = [None]
            exception = [None]
            
            def worker():
                try:
                    result[0] = func(*args, **kwargs)
                except Exception as e:
                    exception[0] = e
            
            thread = threading.Thread(target=worker)
            thread.daemon = True
            thread.start()
            thread.join(seconds)
            
            if thread.is_alive():
                logger.error(f"函数执行超时: {func.__name__}, 超时时间: {seconds}秒")
                raise TimeoutError(f"函数执行超时: {func.__name__}, 超时时间: {seconds}秒")
            
            if exception[0]:
                raise exception[0]
            
            return result[0]
        return wrapper
    return decorator

def parse_project_list(file_path):
    """
    解析项目列表文件
    
    Args:
        file_path: 项目列表文件路径
    
    Returns:
        dict: 解析后的项目结构
    """
    if not os.path.exists(file_path):
        print(f"错误: 项目列表文件 {file_path} 不存在")
        sys.exit(1)
    
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 解析存储目录
    if not lines or not lines[0].startswith('存储目录：'):
        print("错误: 项目列表文件格式不正确，第一行应为存储目录")
        sys.exit(1)
    
    root_dir = lines[0].strip().replace('存储目录：', '')
    
    # 创建项目结构
    project_structure = {
        'root_dir': root_dir,
        'directories': {}
    }
    
    current_level = 0
    current_dirs = [''] * 10  # 假设最多10级目录
    
    for line in lines[1:]:
        line = line.strip()
        if not line:
            continue
        
        # 判断是目录还是项目地址
        if line.startswith('-'):
            # 计算目录级别
            level = 0
            while line.startswith('-'):
                level += 1
                line = line[1:]
            
            # 保存当前级别的目录名
            current_dirs[level-1] = line.strip()
            current_level = level
            
            # 重置更深层级的目录
            for i in range(current_level, len(current_dirs)):
                current_dirs[i] = ''
        else:
            # 项目地址
            # 构建当前完整路径
            path_parts = [current_dirs[i] for i in range(current_level) if current_dirs[i]]
            
            # 在项目结构中创建嵌套目录
            current_dict = project_structure['directories']
            for part in path_parts:
                if part not in current_dict:
                    current_dict[part] = {'directories': {}, 'projects': []}
                current_dict = current_dict[part]['directories']
            
            # 添加项目地址到最后一级目录
            parent_dict = project_structure['directories']
            for i in range(current_level-1):
                parent_dict = parent_dict[current_dirs[i]]['directories']
            
            if current_dirs[current_level-1] not in parent_dict:
                parent_dict[current_dirs[current_level-1]] = {'directories': {}, 'projects': []}
            
            parent_dict[current_dirs[current_level-1]]['projects'].append(line)
    
    return project_structure

def clone_project(url, target_dir, depth=1, timeout=120):
    """
    克隆单个项目（已废弃，保留仅作兼容）
    
    Args:
        url: 项目地址
        target_dir: 目标目录
        depth: 克隆深度，默认为1（浅克隆）
        timeout: 超时时间，默认为120秒
    """
    print("警告: 使用了已废弃的clone_project函数")
    try:
        print(f"正在克隆项目: {url} 到 {target_dir}")
        
        # 设置Git环境变量而不是使用config参数
        env = os.environ.copy()
        env['GIT_HTTP_CONNECT_TIMEOUT'] = str(timeout)
        env['GIT_HTTP_LOW_SPEED_LIMIT'] = '1000'
        env['GIT_HTTP_LOW_SPEED_TIME'] = '30'
        
        # 执行克隆
        print(f"开始克隆，这可能需要一些时间...")
        git.Repo.clone_from(
            url, 
            target_dir,
            # 移除tqdm进度条，避免兼容性问题
            # progress=tqdm(desc=f"克隆 {os.path.basename(target_dir)}", unit="MiB"),
            depth=depth,
            env=env
        )
        print(f"项目克隆成功: {url}")
        return True
    except git.exc.GitCommandError as e:
        if "already exists and is not an empty directory" in str(e):
            print(f"目录已存在且不为空，跳过克隆: {target_dir}")
            return False
        elif "Authentication failed" in str(e):
            print(f"认证失败，请检查GitLab访问凭证: {url}")
            return False
        elif "timeout" in str(e).lower():
            print(f"连接超时，请检查网络连接: {url}")
            return False
        else:
            print(f"项目克隆失败: {url}, 错误: {str(e)}")
            return False
    except Exception as e:
        print(f"项目克隆失败: {url}, 错误: {str(e)}")
        return False

def process_directory(structure, current_path, level=0, args=None):
    """
    递归处理目录结构，克隆项目
    
    Args:
        structure: 当前目录结构
        current_path: 当前路径
        level: 当前目录级别
        args: 命令行参数
    """
    # 用于跟踪当前目录下已存在的项目名称
    existing_project_names = set()
    
    # 处理当前目录下的项目
    for project_url in structure.get('projects', []):
        # 判断是项目还是项目组
        try:
            logger.info(f"开始处理URL: {project_url}")
            result = parser.check_gitlab_url(project_url)
            
            if result.startswith("Project:"):
                # 是项目，直接在当前目录下克隆
                project_name = result.replace("Project: ", "")
                logger.info(f"识别为项目: {project_name}")
                
                # 检查是否在跳过列表中
                if args and args.skip_projects and project_name in [p.strip() for p in args.skip_projects.split(',')]:
                    logger.info(f"项目 {project_name} 在跳过列表中，跳过克隆")
                    continue
                
                # 解析URL路径，获取前一级路径作为可能的前缀
                parsed_url = parser.urlparse(project_url)
                path_parts = parsed_url.path.strip('/').split('/')
                
                # 如果路径至少有两级，取倒数第二级作为前缀
                prefix = ""
                if len(path_parts) >= 2:
                    prefix = path_parts[-2]
                
                # 检查是否需要添加前缀（如果项目名已存在或将要存在）
                final_project_name = project_name
                if project_name in existing_project_names:
                    final_project_name = f"{prefix}-{project_name}"
                    logger.info(f"检测到目录名冲突，使用前缀区分: {final_project_name}")
                
                # 将项目名添加到已存在集合中
                existing_project_names.add(project_name)
                
                target_dir = os.path.join(current_path, final_project_name)
                
                if os.path.exists(target_dir) and args and args.skip_existing:
                    logger.info(f"目录已存在，跳过克隆: {target_dir}")
                    continue
                
                if not os.path.exists(target_dir):
                    os.makedirs(target_dir, exist_ok=True)
                
                # 获取项目详细信息，以便获取SSH URL
                try:
                    gl = parser.get_gitlab_client(project_url)
                    parsed_url = parser.urlparse(project_url)
                    path = parsed_url.path.strip('/')
                    
                    # 尝试获取项目详情
                    try:
                        project_info = gl.projects.get(path)
                        # 根据参数选择使用SSH还是HTTP URL
                        if args and (args.use_ssh or args.prefer_ssh):
                            clone_url = project_info.ssh_url_to_repo
                            logger.info(f"使用SSH URL: {clone_url}")
                        else:
                            clone_url = project_info.http_url_to_repo
                            logger.info(f"使用HTTP URL: {clone_url}")
                    except Exception as e:
                        logger.warning(f"获取项目详情失败，使用原始URL: {str(e)}")
                        clone_url = project_url
                except Exception as e:
                    logger.warning(f"获取GitLab客户端失败，使用原始URL: {str(e)}")
                    clone_url = project_url
                
                # 克隆项目
                try:
                    logger.info(f"正在克隆项目: {clone_url} 到 {target_dir}")
                    
                    # 使用系统git命令克隆
                    if args and args.use_system_git:
                        logger.info(f"使用系统git命令克隆...")
                        
                        # 构建git命令
                        git_cmd = ["git", "clone"]
                        
                        # 添加深度参数（如果不是完整克隆）
                        if not (args and args.full_clone) and args.depth > 0:
                            git_cmd.extend(["--depth", str(args.depth)])
                        
                        # 添加进度参数
                        git_cmd.append("--progress")
                        
                        # 添加URL和目标目录
                        git_cmd.extend([clone_url, target_dir])
                        
                        # 执行命令
                        logger.info(f"执行命令: {' '.join(git_cmd)}")
                        
                        # 使用Popen实时显示输出
                        process = subprocess.Popen(
                            git_cmd,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE,
                            universal_newlines=True,
                            bufsize=1
                        )
                        
                        # 实时显示输出
                        try:
                            while process.poll() is None:
                                # 读取输出
                                stdout_line = process.stdout.readline()
                                if stdout_line:
                                    logger.info(f"Git输出: {stdout_line.strip()}")
                                
                                stderr_line = process.stderr.readline()
                                if stderr_line:
                                    logger.info(f"Git错误: {stderr_line.strip()}")
                                
                                # 短暂休眠，避免CPU占用过高
                                time.sleep(0.1)
                            
                            # 获取剩余输出
                            stdout, stderr = process.communicate()
                            if stdout:
                                logger.info(f"Git输出: {stdout.strip()}")
                            if stderr:
                                logger.info(f"Git错误: {stderr.strip()}")
                            
                            if process.returncode == 0:
                                logger.info(f"项目克隆成功: {clone_url}")
                            else:
                                logger.error(f"项目克隆失败: {clone_url}, 返回码: {process.returncode}")
                                if stderr:
                                    logger.error(f"错误信息: {stderr.strip()}")
                        except KeyboardInterrupt:
                            # 处理用户中断
                            process.terminate()
                            logger.warning(f"用户中断克隆操作: {clone_url}")
                            raise
                        except Exception as e:
                            # 确保进程被终止
                            try:
                                process.terminate()
                            except:
                                pass
                            logger.error(f"克隆过程中发生错误: {str(e)}")
                            raise
                    else:
                        # 使用GitPython克隆
                        # 设置Git环境变量而不是使用config参数
                        env = os.environ.copy()
                        if args:
                            env['GIT_HTTP_CONNECT_TIMEOUT'] = str(args.timeout)
                            env['GIT_HTTP_LOW_SPEED_LIMIT'] = '1000'
                            env['GIT_HTTP_LOW_SPEED_TIME'] = '30'
                        
                        # 构建克隆参数
                        clone_kwargs = {
                            # 移除tqdm进度条，避免兼容性问题
                            # 'progress': tqdm(desc=f"克隆 {project_name}", unit="MiB"),
                        }
                        
                        # 根据参数决定是否使用浅克隆
                        if not (args and args.full_clone):
                            clone_kwargs['depth'] = args.depth if args else 1
                        
                        # 执行克隆
                        logger.info(f"开始克隆 {final_project_name}，这可能需要一些时间...")
                        
                        # 使用超时装饰器包装克隆函数
                        @timeout(args.timeout if args else 120)
                        def clone_with_timeout(url, dir, **kwargs):
                            return git.Repo.clone_from(url, dir, **kwargs)
                        
                        try:
                            clone_with_timeout(
                                clone_url, 
                                target_dir,
                                env=env,
                                **clone_kwargs
                            )
                            logger.info(f"项目克隆成功: {clone_url}")
                        except TimeoutError:
                            logger.error(f"克隆超时: {clone_url}")
                            raise
                except git.exc.GitCommandError as e:
                    if "already exists and is not an empty directory" in str(e):
                        logger.warning(f"目录已存在且不为空，跳过克隆: {target_dir}")
                    elif "Authentication failed" in str(e):
                        logger.error(f"认证失败，请检查GitLab访问凭证: {clone_url}")
                        logger.info(f"如果使用HTTP URL失败，请尝试使用 --use-ssh 参数使用SSH URL")
                    elif "timeout" in str(e).lower():
                        logger.error(f"连接超时，请检查网络连接: {clone_url}")
                    else:
                        logger.error(f"项目克隆失败: {clone_url}, 错误: {str(e)}")
                        logger.info(f"尝试使用 --use-system-git 参数使用系统git命令克隆")
                except TimeoutError as e:
                    logger.error(f"克隆超时: {clone_url}, 错误: {str(e)}")
                    logger.info(f"尝试增加 --timeout 参数值或使用 --use-system-git 参数")
                except Exception as e:
                    logger.error(f"项目克隆失败: {clone_url}, 错误: {str(e)}")
                    logger.info(f"尝试使用 --use-system-git 参数使用系统git命令克隆")
            
            elif result.startswith("Group:"):
                # 是项目组，获取项目组名称
                group_name = result.split(" ")[1]
                logger.info(f"识别为项目组: {group_name}")
                
                # 解析URL路径，获取前一级路径作为可能的前缀
                parsed_url = parser.urlparse(project_url)
                path_parts = parsed_url.path.strip('/').split('/')
                
                # 如果路径至少有两级，取倒数第二级作为前缀
                prefix = ""
                if len(path_parts) >= 2:
                    prefix = path_parts[-2]
                
                # 检查是否需要添加前缀（如果项目组名已存在或将要存在）
                final_group_name = group_name
                if group_name in existing_project_names:
                    final_group_name = f"{prefix}-{group_name}"
                    logger.info(f"检测到目录名冲突，使用前缀区分: {final_group_name}")
                
                # 将项目组名添加到已存在集合中
                existing_project_names.add(group_name)
                
                group_dir = os.path.join(current_path, final_group_name)
                
                if not os.path.exists(group_dir):
                    os.makedirs(group_dir, exist_ok=True)
                
                # 获取项目组下的所有项目
                gl = parser.get_gitlab_client(project_url)
                parsed_url = parser.urlparse(project_url)
                path = parsed_url.path.strip('/')
                
                try:
                    group = gl.groups.get(path)
                    projects = group.projects.list(all=True)
                    
                    logger.info(f"在项目组 {final_group_name} 中找到 {len(projects)} 个项目")
                    
                    # 用于跟踪项目组内已存在的项目名称
                    group_existing_projects = set()
                    
                    for i, project in enumerate(projects):
                        logger.info(f"处理项目 [{i+1}/{len(projects)}]: {project.name}")
                        
                        # 检查是否在跳过列表中
                        if args and args.skip_projects and project.name in [p.strip() for p in args.skip_projects.split(',')]:
                            logger.info(f"项目 {project.name} 在跳过列表中，跳过克隆")
                            continue
                        
                        # 检查是否需要添加前缀（如果项目名已存在或将要存在）
                        final_project_name = project.name
                        if project.name in group_existing_projects:
                            # 从项目路径中提取前缀
                            project_path_parts = project.path_with_namespace.split('/')
                            if len(project_path_parts) >= 2:
                                project_prefix = project_path_parts[-2]
                                final_project_name = f"{project_prefix}-{project.name}"
                                logger.info(f"检测到项目组内目录名冲突，使用前缀区分: {final_project_name}")
                        
                        # 将项目名添加到已存在集合中
                        group_existing_projects.add(project.name)
                        
                        project_dir = os.path.join(group_dir, final_project_name)
                        
                        if os.path.exists(project_dir) and args and args.skip_existing:
                            logger.info(f"目录已存在，跳过克隆: {project_dir}")
                            continue
                        
                        if not os.path.exists(project_dir):
                            os.makedirs(project_dir, exist_ok=True)
                        
                        # 根据参数选择使用SSH还是HTTP URL
                        if args and (args.use_ssh or args.prefer_ssh):
                            clone_url = project.ssh_url_to_repo
                            logger.info(f"使用SSH URL: {clone_url}")
                        else:
                            clone_url = project.http_url_to_repo
                            logger.info(f"使用HTTP URL: {clone_url}")
                        
                        # 克隆项目
                        try:
                            # 使用系统git命令克隆
                            if args and args.use_system_git:
                                logger.info(f"使用系统git命令克隆...")
                                
                                # 构建git命令
                                git_cmd = ["git", "clone"]
                                
                                # 添加深度参数（如果不是完整克隆）
                                if not (args and args.full_clone) and args.depth > 0:
                                    git_cmd.extend(["--depth", str(args.depth)])
                                
                                # 添加进度参数
                                git_cmd.append("--progress")
                                
                                # 添加URL和目标目录
                                git_cmd.extend([clone_url, project_dir])
                                
                                # 执行命令
                                logger.info(f"执行命令: {' '.join(git_cmd)}")
                                
                                # 使用Popen实时显示输出
                                process = subprocess.Popen(
                                    git_cmd,
                                    stdout=subprocess.PIPE,
                                    stderr=subprocess.PIPE,
                                    universal_newlines=True,
                                    bufsize=1
                                )
                                
                                # 实时显示输出
                                try:
                                    while process.poll() is None:
                                        # 读取输出
                                        stdout_line = process.stdout.readline()
                                        if stdout_line:
                                            logger.info(f"Git输出: {stdout_line.strip()}")
                                        
                                        stderr_line = process.stderr.readline()
                                        if stderr_line:
                                            logger.info(f"Git错误: {stderr_line.strip()}")
                                        
                                        # 短暂休眠，避免CPU占用过高
                                        time.sleep(0.1)
                                    
                                    # 获取剩余输出
                                    stdout, stderr = process.communicate()
                                    if stdout:
                                        logger.info(f"Git输出: {stdout.strip()}")
                                    if stderr:
                                        logger.info(f"Git错误: {stderr.strip()}")
                                    
                                    if process.returncode == 0:
                                        logger.info(f"项目克隆成功: {clone_url}")
                                    else:
                                        logger.error(f"项目克隆失败: {clone_url}, 返回码: {process.returncode}")
                                        if stderr:
                                            logger.error(f"错误信息: {stderr.strip()}")
                                except KeyboardInterrupt:
                                    # 处理用户中断
                                    process.terminate()
                                    logger.warning(f"用户中断克隆操作: {clone_url}")
                                    raise
                                except Exception as e:
                                    # 确保进程被终止
                                    try:
                                        process.terminate()
                                    except:
                                        pass
                                    logger.error(f"克隆过程中发生错误: {str(e)}")
                                    raise
                            else:
                                # 使用GitPython克隆
                                # 设置Git环境变量而不是使用config参数
                                env = os.environ.copy()
                                if args:
                                    env['GIT_HTTP_CONNECT_TIMEOUT'] = str(args.timeout)
                                    env['GIT_HTTP_LOW_SPEED_LIMIT'] = '1000'
                                    env['GIT_HTTP_LOW_SPEED_TIME'] = '30'
                                
                                # 构建克隆参数
                                clone_kwargs = {
                                    # 移除tqdm进度条，避免兼容性问题
                                    # 'progress': tqdm(desc=f"克隆 {project.name}", unit="MiB"),
                                }
                                
                                # 根据参数决定是否使用浅克隆
                                if not (args and args.full_clone):
                                    clone_kwargs['depth'] = args.depth if args else 1
                                
                                # 执行克隆
                                logger.info(f"开始克隆 {final_project_name}，这可能需要一些时间...")
                                
                                # 使用超时装饰器包装克隆函数
                                @timeout(args.timeout if args else 120)
                                def clone_with_timeout(url, dir, **kwargs):
                                    return git.Repo.clone_from(url, dir, **kwargs)
                                
                                try:
                                    clone_with_timeout(
                                        clone_url, 
                                        project_dir,
                                        env=env,
                                        **clone_kwargs
                                    )
                                    logger.info(f"项目克隆成功: {clone_url}")
                                except TimeoutError:
                                    logger.error(f"克隆超时: {clone_url}")
                                    raise
                        except git.exc.GitCommandError as e:
                            if "already exists and is not an empty directory" in str(e):
                                logger.warning(f"目录已存在且不为空，跳过克隆: {project_dir}")
                            elif "Authentication failed" in str(e):
                                logger.error(f"认证失败，请检查GitLab访问凭证: {clone_url}")
                                logger.info(f"如果使用HTTP URL失败，请尝试使用 --use-ssh 参数使用SSH URL")
                            elif "timeout" in str(e).lower():
                                logger.error(f"连接超时，请检查网络连接: {clone_url}")
                            else:
                                logger.error(f"项目克隆失败: {clone_url}, 错误: {str(e)}")
                                logger.info(f"尝试使用 --use-system-git 参数使用系统git命令克隆")
                        except TimeoutError as e:
                            logger.error(f"克隆超时: {clone_url}, 错误: {str(e)}")
                            logger.info(f"尝试增加 --timeout 参数值或使用 --use-system-git 参数")
                        except Exception as e:
                            logger.error(f"项目克隆失败: {clone_url}, 错误: {str(e)}")
                            logger.info(f"尝试使用 --use-system-git 参数使用系统git命令克隆")
                
                except Exception as e:
                    logger.error(f"获取项目组项目失败: {str(e)}")
            
            else:
                logger.warning(f"无法确定URL类型，跳过: {project_url}")
        
        except Exception as e:
            logger.error(f"处理URL失败: {project_url}, 错误: {str(e)}")
    
    # 递归处理子目录
    for dir_name, dir_structure in structure.get('directories', {}).items():
        dir_path = os.path.join(current_path, dir_name)
        
        if not os.path.exists(dir_path):
            os.makedirs(dir_path, exist_ok=True)
        
        process_directory(dir_structure, dir_path, level + 1, args)

def main():
    """主函数"""
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(
        description='批量下载GitLab项目代码的工具',
        formatter_class=argparse.RawTextHelpFormatter
    )
    
    parser.add_argument(
        'project_list_file', 
        nargs='?', 
        default='project_list.txt',
        help='项目列表文件路径，默认为当前目录下的project_list.txt'
    )
    
    parser.add_argument(
        '-v', '--version', 
        action='version', 
        version='GitLab项目批量克隆工具 v1.0.0'
    )
    
    parser.add_argument(
        '--dry-run', 
        action='store_true', 
        help='仅解析项目列表文件，不执行实际克隆操作'
    )
    
    parser.add_argument(
        '--timeout', 
        type=int,
        default=120,
        help='Git克隆操作的超时时间（秒），默认为120秒'
    )
    
    parser.add_argument(
        '--depth',
        type=int,
        default=0,
        help='Git浅克隆的深度，默认为0（完整克隆）'
    )
    
    parser.add_argument(
        '--full-clone',
        action='store_true',
        default=True,
        help='执行完整克隆，不使用浅克隆（默认启用）'
    )
    
    parser.add_argument(
        '--no-full-clone',
        action='store_false',
        dest='full_clone',
        help='不执行完整克隆，使用浅克隆'
    )
    
    parser.add_argument(
        '--skip-existing',
        action='store_true',
        default=True,
        help='跳过已存在的目录，不尝试克隆（默认启用）'
    )
    
    parser.add_argument(
        '--no-skip-existing',
        action='store_false',
        dest='skip_existing',
        help='不跳过已存在的目录，尝试克隆'
    )
    
    parser.add_argument(
        '--use-ssh',
        action='store_true',
        help='使用SSH URL而不是HTTP URL进行克隆'
    )
    
    parser.add_argument(
        '--prefer-ssh',
        action='store_true',
        default=True,
        help='优先使用SSH URL进行克隆，如果失败则尝试HTTP URL（默认启用）'
    )
    
    parser.add_argument(
        '--no-prefer-ssh',
        action='store_false',
        dest='prefer_ssh',
        help='不优先使用SSH URL进行克隆'
    )
    
    parser.add_argument(
        '--use-system-git',
        action='store_true',
        default=True,
        help='使用系统git命令而不是GitPython库进行克隆（默认启用）'
    )
    
    parser.add_argument(
        '--no-use-system-git',
        action='store_false',
        dest='use_system_git',
        help='不使用系统git命令，使用GitPython库进行克隆'
    )
    
    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
        default='INFO',
        help='日志级别，默认为INFO'
    )
    
    parser.add_argument(
        '--skip-projects',
        type=str,
        help='跳过特定的项目，多个项目名称用逗号分隔，例如：project1,project2'
    )
    
    # 解析命令行参数
    args = parser.parse_args()
    
    # 设置日志级别
    logger.setLevel(getattr(logging, args.log_level))
    
    logger.info(f"使用项目列表文件: {args.project_list_file}")
    
    # 解析项目列表文件
    project_structure = parse_project_list(args.project_list_file)
    
    # 创建根目录
    root_dir = project_structure['root_dir']
    if not os.path.exists(root_dir):
        os.makedirs(root_dir, exist_ok=True)
    
    logger.info(f"项目将被克隆到: {os.path.abspath(root_dir)}")
    
    # 处理要跳过的项目
    skip_projects = []
    if args.skip_projects:
        skip_projects = [p.strip() for p in args.skip_projects.split(',')]
        logger.info(f"将跳过以下项目: {', '.join(skip_projects)}")
    
    # 如果是dry-run模式，打印解析结果并退出
    if args.dry_run:
        logger.info("\n=== 干运行模式，不执行实际克隆操作 ===")
        logger.info("解析结果:")
        logger.info(f"根目录: {root_dir}")
        
        def print_structure(structure, indent=0):
            for dir_name, dir_structure in structure.get('directories', {}).items():
                logger.info("  " * indent + f"- {dir_name}/")
                
                for project in dir_structure.get('projects', []):
                    logger.info("  " * (indent + 1) + f"* {project}")
                
                print_structure(dir_structure, indent + 1)
        
        print_structure(project_structure)
        return
    
    # 设置全局Git环境变量
    os.environ['GIT_HTTP_CONNECT_TIMEOUT'] = str(args.timeout)
    os.environ['GIT_HTTP_LOW_SPEED_LIMIT'] = '1000'  # 1 KB/s
    os.environ['GIT_HTTP_LOW_SPEED_TIME'] = '30'  # 30秒
    
    logger.info(f"已设置Git超时参数: 连接超时={args.timeout}秒")
    
    # 处理目录结构
    process_directory(
        {'directories': project_structure['directories']}, 
        root_dir,
        args=args
    )
    
    logger.info("批量克隆完成！")

if __name__ == "__main__":
    main() 