# GitLab配置
# 注意: JSON字符串中不能有换行，必须是一行
export GITLAB_CONFIGS='{
    "gitlab.wandatech-dev.com": {
        "url": "http://gitlab.wandatech-dev.com",
        "token": "你的token1"
    },
    "gitlab.mx.com": {
        "url": "http://gitlab.mx.com",
        "token": "你的token2"
    }
}'

# 为了安全起见，建议将token存储为单独的环境变量
export GITLAB_TOKEN_1="你的token1"
export GITLAB_TOKEN_2="你的token2"

# 然后在GITLAB_CONFIGS中引用这些环境变量
export GITLAB_CONFIGS="{
    \"gitlab.wandatech-dev.com\": {
        \"url\": \"http://gitlab.wandatech-dev.com\",
        \"token\": \"${GITLAB_TOKEN_1}\"
    },
    \"gitlab.mx.com\": {
        \"url\": \"http://gitlab.mx.com\",
        \"token\": \"${GITLAB_TOKEN_2}\"
    }
}" 